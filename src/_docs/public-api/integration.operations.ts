import { applyDecorators } from "@nestjs/common";
import { ApiBody, ApiConsumes, ApiOperation, ApiProduces, ApiResponse } from "@nestjs/swagger";
import { PortfolioItemUpdatedRequestDTO } from "@integration-hub/application/dto/in/portfolio-item-updated-request.dto";

export class IntegrationOperations {
  static executeIntegration() {
    return applyDecorators(
      ApiOperation({
        summary: 'Execute integration strategy',
        description: 'This endpoint execute an integration strategy based on customer preferences',
      }),
      ApiProduces('application/json'),
      ApiConsumes('application/json'),
      ApiBody({
        type: PortfolioItemUpdatedRequestDTO,
        description: 'Portfolio item updated request',
        examples: {
          example: {
            summary: 'Portfolio item updated request',
            value: {
              customerId: '123e4567-e89b-12d3-a456-426614174000',
              portfolioItemId: '123e4567-e89b-12d3-a456-426614174000',
              portfolioId: '123e4567-e89b-12d3-a456-426614174000',
              workflowId: '123e4567-e89b-12d3-a456-426614174000',
              currentStatus: 'FINISHED',
            },
          },
        },
      }),
      ApiResponse({
        status: 200,
        description: 'Integration executed successfully',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            data: { type: 'object' },
          },
        },
      }),
      ApiResponse({
        status: 400,
        description: 'Bad Request',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            message: { type: 'string' },
          },
        },
      }),
      ApiResponse({
        status: 404,
        description: 'Not Found',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            message: { type: 'string' },
          },
        },
      }),
      ApiResponse({
        status: 500,
        description: 'Internal Server Error',
        schema: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            message: { type: 'string' },
          },
        },
      }),
    );
  }
}