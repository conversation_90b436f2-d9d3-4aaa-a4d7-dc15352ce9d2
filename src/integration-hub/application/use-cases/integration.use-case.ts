import { Inject, Injectable } from '@nestjs/common';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { BusinessBasePort } from '@integration-hub/infrastructure/ports/http/business-base.port';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationStrategyManager } from '@integration-hub/domain/services/integration-strategy.manager';
import { IntegrationResult } from '@integration-hub/domain/entities/integration-result.entity';
import { CustomerChannelIntegrationDataDefinitionPort } from '@common/auth/db/ports/customer-channel-integration-data-definition.port';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class IntegrationUseCase {
  constructor(
    @Inject('BusinessBasePort')
    private readonly businessBaseAdapter: BusinessBasePort,
    private readonly integrationStrategyManager: IntegrationStrategyManager,
    @Inject('CustomerChannelIntegrationDataDefinitionPort')
    private readonly customerChannelIntegrationDataDefinitionAdapter: CustomerChannelIntegrationDataDefinitionPort,
  ) {}

  async executeIntegrationStrategy(portfolioItemUpdatedRequestDTO: PortfolioItemUpdatedRequestDTO): Promise<IntegrationResult> {
    const customerChannelIntegrationDataDefinition = await this.customerChannelIntegrationDataDefinitionAdapter.get(portfolioItemUpdatedRequestDTO.customerId);

    if (!customerChannelIntegrationDataDefinition?.data?.integrationConfig) {
      throw new BusinessException(
        'IntegrationUseCase',
        'No integration config found',
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const integrationContext = new IntegrationContext(
      portfolioItemUpdatedRequestDTO.customerId,
      portfolioItemUpdatedRequestDTO.portfolioItemId,
      portfolioItemUpdatedRequestDTO.portfolioId,
      portfolioItemUpdatedRequestDTO.workflowId,
      portfolioItemUpdatedRequestDTO.currentStatus,
      customerChannelIntegrationDataDefinition.data.integrationConfig,
    );

    const result = await this.integrationStrategyManager.executeStrategy(integrationContext);
    return result;
  }
}