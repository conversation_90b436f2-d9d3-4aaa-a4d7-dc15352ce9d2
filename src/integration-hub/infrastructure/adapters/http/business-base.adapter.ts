import { Injectable } from '@nestjs/common';
import { BusinessBasePort } from "@integration-hub/infrastructure/ports/http/business-base.port";
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { logger } from '@edutalent/commons-sdk';
import { handleHttpError } from '@common/utils/handle-http-error';
import { SendDirectMessageDto } from '@integration-hub/infrastructure/dto/out/send-direct-message.dto';
import FormData from 'form-data';
import { createReadStream } from 'fs';

@Injectable()
export class InfraBusinessBaseAdapter implements BusinessBasePort {
  private readonly businessBaseServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.businessBaseServiceUrl = process.env.BUSINESS_BASE_SERVICE_URL.toString();
  }

async sendDirectMessage(
  portfolioItemId: string,
  dto: SendDirectMessageDto,
  file: Express.Multer.File,
): Promise<void> {
  try {
    const url = `${this.businessBaseServiceUrl}/api/v1/business-base/portfolio-items/${portfolioItemId}/conversation-history`;

    const formData = new FormData();
    formData.append('message', dto.message);
    formData.append('messageType', dto.messageType);
    formData.append('roleType', dto.roleType);

    if (file) {
      formData.append('file', createReadStream(file.path), file.originalname);
    }

    const headers = {
      ...formData.getHeaders(),
      // Authorization: `Bearer ${token}`
    };

    await =lastValueFrom(
      this.httpService.post(url, formData, { headers }),
    );
  } catch (error) {
    handleHttpError(error, 'Infra-BusinessBase-adapter');
  }
}
}